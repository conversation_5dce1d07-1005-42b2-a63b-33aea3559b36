<script lang="ts">
	import { page } from '$app/state';

	let pillIndicatorElement: HTMLSpanElement;

	const navItems = [
		{ href: '/', label: 'Home' },
		{ href: '/o-nas', label: 'O nás' },
		{ href: '/cenik', label: '<PERSON>n<PERSON>' },
		{ href: '/kontakt', label: 'Kontakt' },
		{ href: '/stan-se-lektorem', label: 'Staň se lektorem' }
	];

	function updatePillIndicator() {

	}

	const handleMouseEnter = (event: MouseEvent) => {
	};

	const handleMouseLeave = () => {
	};

	$: console.log(page.url.pathname);
</script>

<nav class="header-nav">
	{#each navItems as item}
		<a href={item.href}
		   onmouseenter="{handleMouseEnter}"
		   onmouseleave="{handleMouseLeave}"
		>
			{item.label}
		</a>
	{/each}

	<span class="pill-indicator" bind:this={pillIndicatorElement}></span>
</nav>

<style lang="scss">
	.header-nav {
		width: 100%;
		background-color: var(--color-dark);
		height: var(--header-height);
		border-radius: calc(var(--header-height) / 2);
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 0 var(--spacing-m);

		a {
			color: var(--color-light);
			text-decoration: none;
			padding: 0 var(--spacing-m);
		}
	}
</style>
